<!doctype html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Calendar Event Demo - Beautiful Design</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem;
      }

      .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .demo-header {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        padding: 2rem;
        text-align: center;
      }

      .demo-header h1 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        font-weight: 700;
      }

      .demo-header p {
        opacity: 0.9;
        font-size: 1.1rem;
      }

      .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        background: #e5e7eb;
        padding: 1px;
      }

      .day-header {
        background: #f8fafc;
        padding: 1rem;
        text-align: center;
        font-weight: 600;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-size: 0.875rem;
      }

      .day-cell {
        background: white;
        min-height: 120px;
        padding: 0.75rem;
        position: relative;
        transition: all 0.3s ease;
      }

      .day-cell:hover {
        background: #f8fafc;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .day-number {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.75rem;
      }

      .today .day-number {
        background: #ef4444;
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
      }

      /* Event styles matching the improved calendar */
      .demo-event {
        border-radius: 8px;
        padding: 0.4rem 0.6rem;
        font-size: 0.75rem;
        cursor: pointer;
        border: none;
        margin: 2px 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        min-height: 24px;
        overflow: hidden;
        line-height: 1.3;
        animation: eventSlideIn 0.5s ease-out;
        backdrop-filter: blur(8px);
        border-left: 4px solid rgba(255, 255, 255, 0.3);
        box-shadow:
          0 2px 8px rgba(0, 0, 0, 0.1),
          0 1px 3px rgba(0, 0, 0, 0.08),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
      }

      .demo-event::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.15) 0%,
          rgba(255, 255, 255, 0.05) 50%,
          rgba(0, 0, 0, 0.05) 100%
        );
        pointer-events: none;
        border-radius: inherit;
      }

      .demo-event::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.4) 50%,
          transparent 100%
        );
        transition: left 0.8s ease;
        pointer-events: none;
      }

      .demo-event:hover {
        transform: translateY(-3px) scale(1.03);
        box-shadow:
          0 12px 32px rgba(0, 0, 0, 0.15),
          0 4px 16px rgba(0, 0, 0, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.3);
        z-index: 10;
        border-left-color: rgba(255, 255, 255, 0.6);
      }

      .demo-event:hover::after {
        left: 100%;
      }

      .event-time {
        font-weight: 700;
        font-size: 0.65rem;
        letter-spacing: 0.03em;
        opacity: 0.9;
        line-height: 1.2;
        margin-bottom: 2px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
      }

      .event-title {
        font-weight: 600;
        font-size: 0.75rem;
        line-height: 1.2;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 2;
      }

      /* Event types */
      .event-task {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-left-color: #60a5fa;
        color: white;
      }

      .event-task .event-time::before {
        content: '📋 ';
        margin-right: 0.25rem;
      }

      .event-report {
        background: linear-gradient(135deg, #10b981 0%, #047857 100%);
        border-left-color: #34d399;
        color: white;
      }

      .event-report .event-time::before {
        content: '📊 ';
        margin-right: 0.25rem;
      }

      .event-reminder {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border-left-color: #fbbf24;
        color: white;
      }

      .event-reminder .event-time::before {
        content: '⏰ ';
        margin-right: 0.25rem;
      }

      .event-meeting {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border-left-color: #a78bfa;
        color: white;
      }

      .event-meeting .event-time::before {
        content: '👥 ';
        margin-right: 0.25rem;
      }

      .event-deadline {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        border-left-color: #f87171;
        color: white;
      }

      .event-deadline .event-time::before {
        content: '🚨 ';
        margin-right: 0.25rem;
      }

      /* Priority indicators */
      .priority-high {
        border-left-width: 6px;
        font-weight: 700;
      }

      .priority-high::after {
        content: '🔥';
        position: absolute;
        top: 2px;
        right: 4px;
        font-size: 0.6rem;
        z-index: 3;
        background: transparent !important;
        border: none !important;
        color: inherit !important;
      }

      .priority-medium {
        border-left-width: 4px;
        font-weight: 600;
      }

      .priority-low {
        border-left-width: 2px;
        font-weight: 500;
        opacity: 0.9;
      }

      @keyframes eventSlideIn {
        from {
          opacity: 0;
          transform: translateX(-15px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateX(0) scale(1);
        }
      }

      .demo-footer {
        padding: 2rem;
        background: #f8fafc;
        text-align: center;
        color: #64748b;
      }

      .legend {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
        margin-bottom: 1rem;
      }

      .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
      }

      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <div class="demo-container">
      <div class="demo-header">
        <h1>🗓️ Beautiful Calendar Events</h1>
        <p>Giao diện sự kiện calendar được cải tiến với hiệu ứng đẹp mắt và thẩm mỹ</p>
      </div>

      <div class="calendar-grid">
        <!-- Headers -->
        <div class="day-header">T2</div>
        <div class="day-header">T3</div>
        <div class="day-header">T4</div>
        <div class="day-header">T5</div>
        <div class="day-header">T6</div>
        <div class="day-header">T7</div>
        <div class="day-header">CN</div>

        <!-- Week 1 -->
        <div class="day-cell">
          <div class="day-number">28</div>
          <div class="demo-event event-reminder priority-medium">
            <div class="event-time">09:00</div>
            <div class="event-title">Họp team</div>
          </div>
        </div>
        <div class="day-cell">
          <div class="day-number">29</div>
          <div class="demo-event event-task priority-high">
            <div class="event-time">14:00</div>
            <div class="event-title">Hoàn thành báo cáo</div>
          </div>
        </div>
        <div class="day-cell today">
          <div class="day-number">30</div>
          <div class="demo-event event-meeting priority-high">
            <div class="event-time">10:00</div>
            <div class="event-title">Meeting khách hàng</div>
          </div>
          <div class="demo-event event-deadline priority-high">
            <div class="event-time">16:00</div>
            <div class="event-title">Deadline dự án</div>
          </div>
        </div>
        <div class="day-cell">
          <div class="day-number">31</div>
          <div class="demo-event event-report priority-medium">
            <div class="event-time">11:00</div>
            <div class="event-title">Báo cáo tuần</div>
          </div>
        </div>
        <div class="day-cell">
          <div class="day-number">1</div>
          <div class="demo-event event-task priority-low">
            <div class="event-time">15:00</div>
            <div class="event-title">Review code</div>
          </div>
        </div>
        <div class="day-cell">
          <div class="day-number">2</div>
        </div>
        <div class="day-cell">
          <div class="day-number">3</div>
          <div class="demo-event event-reminder priority-medium">
            <div class="event-time">12:00</div>
            <div class="event-title">Nghỉ trưa</div>
          </div>
        </div>
      </div>

      <div class="demo-footer">
        <div class="legend">
          <div class="legend-item">
            <div
              class="legend-color"
              style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)"
            ></div>
            <span>📋 Nhiệm vụ</span>
          </div>
          <div class="legend-item">
            <div
              class="legend-color"
              style="background: linear-gradient(135deg, #10b981 0%, #047857 100%)"
            ></div>
            <span>📊 Báo cáo</span>
          </div>
          <div class="legend-item">
            <div
              class="legend-color"
              style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%)"
            ></div>
            <span>⏰ Nhắc nhở</span>
          </div>
          <div class="legend-item">
            <div
              class="legend-color"
              style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)"
            ></div>
            <span>👥 Họp</span>
          </div>
          <div class="legend-item">
            <div
              class="legend-color"
              style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%)"
            ></div>
            <span>🚨 Deadline</span>
          </div>
        </div>
        <p>✨ Hover vào các sự kiện để xem hiệu ứng đẹp mắt!</p>
      </div>
    </div>
  </body>
</html>

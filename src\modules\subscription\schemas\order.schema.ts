/**
 * Schema cho form thông tin đơn hàng subscription
 */
import { z } from 'zod';
import { TFunction } from 'i18next';
import { ValidationSchemas } from '@/shared/validation/schemas';

/**
 * Enum cho loại thông tin xuất hóa đơn
 */
export enum InvoiceType {
  PERSONAL = 'personal',
  BUSINESS = 'business',
}

/**
 * Schema factory cho thông tin xuất hóa đơn với hỗ trợ i18n
 */
export const createInvoiceSchemas = (t: TFunction) => {
  /**
   * Schema cho thông tin xuất hóa đơn cá nhân
   */
  const personalInvoiceSchema = z.object({
    type: z.literal(InvoiceType.PERSONAL),
    fullName: z
      .string()
      .min(1, t('subscription:validation.fullName.required', 'Vui lòng nhập họ và tên')),
    phoneNumber: z
      .string()
      .min(1, t('subscription:validation.phoneNumber.required', 'Vui lòng nhập số điện thoại')),
    address: z
      .string()
      .min(1, t('subscription:validation.address.required', 'Vui lòng nhập địa chỉ')),
    email: z.string().email(t('subscription:validation.email.invalid', 'Email không hợp lệ')),
    dateOfBirth: ValidationSchemas.flexibleDate().optional(),
    gender: z.string().optional(),
  });

  /**
   * Schema cho thông tin xuất hóa đơn doanh nghiệp
   */
  const businessInvoiceSchema = z.object({
    type: z.literal(InvoiceType.BUSINESS),
    representativeName: z
      .string()
      .min(
        1,
        t('subscription:validation.representativeName.required', 'Vui lòng nhập tên người đại diện')
      ),
    representativePosition: z
      .string()
      .min(
        1,
        t(
          'subscription:validation.representativePosition.required',
          'Vui lòng nhập vị trí người đại diện'
        )
      )
      .min(
        2,
        t(
          'subscription:validation.representativePosition.minLength',
          'Vị trí phải có ít nhất 2 ký tự'
        )
      ),
    companyName: z
      .string()
      .min(1, t('subscription:validation.companyName.required', 'Vui lòng nhập tên doanh nghiệp')),
    companyAddress: z
      .string()
      .min(
        1,
        t('subscription:validation.companyAddress.required', 'Vui lòng nhập địa chỉ doanh nghiệp')
      ),
    taxCode: z
      .string()
      .min(1, t('subscription:validation.taxCode.required', 'Mã số thuế là bắt buộc'))
      .regex(
        /^[0-9]{10,13}$/,
        t(
          'subscription:validation.taxCode.invalid',
          'Mã số thuế chỉ được chứa số và có độ dài từ 10-13 chữ số'
        )
      ),
    email: z.string().email(t('subscription:validation.email.invalid', 'Email không hợp lệ')),
  });

  return {
    personalInvoiceSchema,
    businessInvoiceSchema,
  };
};

/**
 * Schema factory cho form đơn hàng subscription với hỗ trợ i18n
 */
export const createSubscriptionOrderFormSchema = (t: TFunction) => {
  const { personalInvoiceSchema, businessInvoiceSchema } = createInvoiceSchemas(t);

  const invoiceInfoSchema = z.discriminatedUnion('type', [
    personalInvoiceSchema,
    businessInvoiceSchema,
  ]);

  const subscriptionOrderFormSchema = z.object({
    couponCode: z.string().optional(),
    invoiceInfo: invoiceInfoSchema,
  });

  return {
    subscriptionOrderFormSchema,
    invoiceInfoSchema,
    personalInvoiceSchema,
    businessInvoiceSchema,
  };
};

// Backward compatibility - export static schemas
export const personalInvoiceSchema = z.object({
  type: z.literal(InvoiceType.PERSONAL),
  fullName: z.string().min(1, 'Vui lòng nhập họ và tên'),
  phoneNumber: z.string().min(1, 'Vui lòng nhập số điện thoại'),
  address: z.string().min(1, 'Vui lòng nhập địa chỉ'),
  email: z.string().email('Email không hợp lệ'),
  dateOfBirth: ValidationSchemas.flexibleDate().optional(),
  gender: z.string().optional(),
});

export const businessInvoiceSchema = z.object({
  type: z.literal(InvoiceType.BUSINESS),
  representativeName: z.string().min(1, 'Vui lòng nhập tên người đại diện'),
  representativePosition: z
    .string()
    .min(1, 'Vui lòng nhập vị trí người đại diện')
    .refine(
      value => {
        const words = value.trim().split(/\s+/);
        return words.length >= 2;
      },
      {
        message: 'Vị trí người đại diện phải có ít nhất 2 từ',
      }
    ),
  companyName: z.string().min(1, 'Vui lòng nhập tên doanh nghiệp'),
  companyAddress: z.string().min(1, 'Vui lòng nhập địa chỉ doanh nghiệp'),
  taxCode: z
    .string()
    .min(1, 'Mã số thuế là bắt buộc')
    .regex(/^[0-9]{10,13}$/, 'Mã số thuế chỉ được chứa số và có độ dài từ 10-13 chữ số'),
  email: z.string().email('Email không hợp lệ'),
});

/**
 * Schema cho thông tin xuất hóa đơn (union type)
 */
export const invoiceInfoSchema = z.discriminatedUnion('type', [
  personalInvoiceSchema,
  businessInvoiceSchema,
]);

/**
 * Schema cho form đơn hàng subscription
 */
export const subscriptionOrderFormSchema = z.object({
  couponCode: z.string().optional(),
  invoiceInfo: invoiceInfoSchema,
});

/**
 * Kiểu dữ liệu từ schema
 */
export type SubscriptionOrderFormValues = z.infer<typeof subscriptionOrderFormSchema>;
export type InvoiceInfoValues = z.infer<typeof invoiceInfoSchema>;
export type PersonalInvoiceValues = z.infer<typeof personalInvoiceSchema>;
export type BusinessInvoiceValues = z.infer<typeof businessInvoiceSchema>;

/**
 * Giá trị mặc định cho form
 */
export const defaultSubscriptionOrderValues: Partial<SubscriptionOrderFormValues> = {
  couponCode: '',
  invoiceInfo: {
    type: InvoiceType.PERSONAL,
    fullName: '',
    phoneNumber: '',
    address: '',
    email: '',
  },
};

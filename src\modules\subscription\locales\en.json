{"subscription": {"title": "Service Packages", "subtitle": "Choose a service package that suits your needs", "description": "Choose a service package that suits your needs", "serviceType": "Service Type", "billingPeriod": "Billing Period", "noResults": "No service packages found", "tryDifferentSearch": "Please try a different search", "mainPackagesDescription": "Main packages with all features for every use case", "featurePackagesDescription": "Additional feature packages to expand system capabilities", "chooseThisPlan": "Choose this plan", "types": {"main": "Main Packages", "feature": "Features"}, "duration": {"title": "Choose Subscription Duration", "WEEKLY": "Weekly", "monthly": "Monthly", "semi_annual": "6 Months", "annual": "Annual", "save": "Save {{percent}}%", "tags": {"monthly": "Flexible", "semi_annual": "Save 20%", "annual": "Best Value - Save 40%"}, "descriptions": {"monthly": "Pay monthly, cancel anytime", "semi_annual": "Pay every 6 months, save more", "annual": "Pay annually, maximum savings and benefits"}, "popular": "Most Popular", "bestValue": "Best Value", "mostFlexible": "Most Flexible"}, "serviceTypes": {"main": "Main", "feature": "Feature Packages", "TRIAL": "Trial Package"}, "packages": {"popular": "Popular", "recommended": "Recommended", "basic": "Basic", "pro": "Professional", "enterprise": "Enterprise", "custom": "Custom", "perMonth": "/month", "selectPackage": "Select Package", "currentPlan": "Current Plan", "upgrade": "Upgrade", "features": {"Số lượng người dùng": "Number of users", "Dung lượng lưu trữ": "Storage capacity", "Hỗ trợ kỹ thuật": "Technical support", "Báo cáo cơ bản": "Basic reports", "Báo cáo nâng cao": "Advanced reports", "Báo cáo tùy chỉnh": "Custom reports", "Tích hợp API": "API integration", "Quản lý quyền": "Permission management", "Quản lý quyền nâng cao": "Advanced permission management", "Tùy chỉnh theo yêu cầu": "Custom requirements", "Số lượng cuộc hội thoại": "Number of conversations", "Số lượng nhân viên": "Number of staff", "Chatbot nâng cao": "Advanced chatbot", "Tích hợp website": "Website integration", "Tích hợp mạng xã hội": "Social media integration", "Phân tích hội thoại": "Conversation analytics", "Số lượng giao dịch": "Number of transactions", "Phí giao dịch": "Transaction fee", "Phương thức thanh toán": "Payment methods", "Thanh toán định kỳ": "Recurring payments", "Số lượng email": "Number of emails", "Số lượng chiến dịch": "Number of campaigns", "Mẫu email": "Email templates", "Phân tích chi tiết": "Detailed analytics", "Tự động hóa": "Automation", "Phân đoạn khách hàng": "Customer segmentation", "Không giới hạn": "Unlimited"}, "included": "Included", "notIncluded": "Not included", "description": {"main-basic": "Basic service package for small businesses", "main-pro": "Professional service package for medium businesses", "main-enterprise": "Enterprise service package for large businesses", "feature-chat": "Live chat feature package for businesses", "feature-payment": "Payment feature package for businesses", "feature-marketing": "Marketing feature package for businesses"}}, "order": {"title": "Order", "summary": "Order Summary", "selectedPackage": "Selected Package", "packageName": "Package Name", "duration": "Duration", "price": "Price", "discount": "Discount", "total": "Total", "paymentMethod": {"title": "Payment Method", "bankTransfer": "Bank Transfer", "bankTransferDesc": "Pay by scanning QR code or bank transfer", "creditCard": "Credit/Debit Card", "creditCardDesc": "Pay with Visa, Mastercard, JCB", "eWallet": "E-Wallet", "eWalletDesc": "Pay via Momo, ZaloPay, VNPay", "comingSoon": "Coming Soon"}, "bankTransfer": "Bank Transfer", "creditCard": "Credit Card", "eWallet": "E-Wallet", "rPoint": "R-Point", "rPointPayment": "Pay with R-Point", "availableRPoints": "Available R-Points", "requiredRPoints": "Required R-Points", "rPointDescription": "Payment will be processed immediately when you click the \"Pay\" button below.", "payWithRPoint": "Pay with R-Point", "packageDetails": "Package Details", "packagePrice": "Package Price", "packageType": "Package Type", "orderSummary": "Order Summary", "couponCode": "Coupon Code", "applyCoupon": "Apply", "checkout": "Checkout", "back": "Back", "scanQRCode": "Scan QR code to pay", "bankName": "Bank", "accountNumber": "Account Number", "accountName": "Account Name", "invoiceInfo": "Invoice Information", "personalInvoice": "Personal Invoice", "businessInvoice": "Business Invoice", "taxCode": "Tax Code", "companyName": "Company Name", "companyAddress": "Company Address", "invoice": {"title": "Invoice Information", "personal": "Personal", "business": "Business", "fullName": "Full Name", "fullNamePlaceholder": "Enter full name", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter phone number", "address": "Address", "addressPlaceholder": "Enter address", "email": "Email", "emailPlaceholder": "Enter email", "representativeName": "Representative Name", "representativeNamePlaceholder": "Enter representative name", "representativePosition": "Position", "representativePositionPlaceholder": "Enter position", "companyName": "Company Name", "companyNamePlaceholder": "Enter company name", "companyAddress": "Company Address", "companyAddressPlaceholder": "Enter company address", "taxCode": "Tax Code", "taxCodePlaceholder": "Enter tax code"}, "orderSuccess": "Order Successful", "orderSuccessMessage": "Thank you for your order. We will process your order as soon as possible."}, "payment": {"title": "Payment", "processing": "Processing payment", "success": {"title": "Payment Successful!", "description": "Service package has been activated successfully.", "backToPackages": "View Packages", "viewProfile": "View Account", "viewUsage": "View Usage", "viewSubscriptionUsageAria": "View Subscription Usage"}, "failed": "Payment failed", "orderNumber": "Order Number", "paymentDate": "Payment Date", "paymentMethod": "Payment Method", "amount": "Amount", "status": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}, "backToPackages": "Back to packages", "total": "Total", "orderDetails": "Order Details", "viewDetails": "View Order Details", "backToHome": "Back to Home", "rPoint": "R-Point", "bankTransfer": {"title": "Bank Transfer Information", "description": "Please transfer according to the information below to complete payment", "amount": "Amount", "bankName": "Bank", "accountNumber": "Account Number", "accountHolder": "Account Holder", "transferContent": "Transfer Content", "copyAccount": "Copy account number", "copyContent": "Copy transfer content"}, "creditCard": "Credit Card", "eWallet": "E-Wallet", "paymentNotFound": "Payment not found", "paymentNotFoundDesc": "Payment does not exist or has been deleted", "invalidTransactionId": "Invalid transaction ID", "invalidTransactionIdDesc": "Please check the URL again", "paymentSummary": "Payment Summary", "paymentId": "Payment ID", "referenceCode": "Reference Code", "description": "Description", "createdAt": "Created At", "planPricingId": "Service Package", "qrPayment": {"title": "Payment Method", "scanQR": "Scan QR code to pay", "qrCodeAlt": "QR Code", "instruction": "Use your banking app to scan the QR code"}}, "defaultDescription": "Enhance work efficiency with advanced AI technology", "bottomCta": "Upgrade now to experience the latest features!", "badges": {"popular": "Most Popular", "recommended": "Recommended", "new": "New", "bestSeller": "Best Seller", "limitedTime": "Limited Time"}, "cta": {"buyNowSave": "Buy Now - Save {{percent}}%", "selectPackage": "Select {{name}} Package", "getStarted": "Get Started", "contactSales": "Contact Sales", "upgradeNow": "Upgrade Now"}, "features": {"technicalSupport": "Professional technical support", "latestUpdates": "Latest feature updates", "dataSecurity": "Advanced data security", "apiIntegration": "Flexible API integration", "unlimitedUsers": "Unlimited users", "prioritySupport": "Priority support", "advancedReports": "Advanced reports", "customization": "Interface customization"}, "socialProof": {"users": "{{count}} users", "reviews": "{{count}} reviews", "rating": "{{rating}} stars", "stockLeft": "{{count}} left"}, "validation": {"representativeName": {"required": "Please enter representative name"}, "representativePosition": {"required": "Please enter representative position", "minLength": "Position must be at least 2 characters", "minWords": "Representative position must have at least 2 words"}, "companyName": {"required": "Please enter company name"}, "companyAddress": {"required": "Please enter company address"}, "taxCode": {"required": "Tax code is required", "invalid": "Tax code must contain only numbers and be 10-13 digits long"}, "email": {"required": "Please enter email", "invalid": "Invalid email"}}, "error": {"title": "Data Loading Error", "message": "Unable to load service packages. Please try again later.", "retry": "Retry"}}}
/**
 * Trang đặt hàng gói dịch vụ
 */
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';

import { Typography, Button, Card, Icon, Form } from '@/shared/components/common';
import { OrderSummary } from '../components';
import InvoiceInfo from '../components/InvoiceInfo';
import PaymentMethodCard from '../components/PaymentMethodCard';
import { ServiceType, SubscriptionDuration, InvoiceType } from '../types';
import { useSubscriptionPaymentV2 } from '../hooks/useSubscriptionPaymentV2';
import {
  createSubscriptionOrderFormSchema,
  defaultSubscriptionOrderValues,
} from '../schemas/order.schema';
import { formatPrice } from '../utils/subscription.utils';
import { useCurrentUser, useBusinessInfo } from '@/modules/profile/hooks/useUser';
import PageWrapper from '@/shared/components/common/PageWrapper';

/**
 * Thông tin gói dịch vụ đã chọn
 */
interface SelectedPackageInfo {
  id: string;
  name: string;
  type: ServiceType;
  price: number;
  duration: SubscriptionDuration;
  planPricingId?: number; // ID của plan pricing để gọi API mới
}

/**
 * Trang đặt hàng gói dịch vụ
 */
const SubscriptionOrderPage: React.FC = () => {
  const { t } = useTranslation(['subscription']);
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = location;

  // Tạo schema với translation
  const { subscriptionOrderFormSchema } = useMemo(() => createSubscriptionOrderFormSchema(t), [t]);

  // Lấy thông tin gói dịch vụ từ state
  const packageInfo: SelectedPackageInfo = state?.packageInfo || {
    id: '',
    name: '',
    type: ServiceType.MAIN,
    price: 0,
    duration: SubscriptionDuration.MONTHLY,
    planPricingId: undefined,
  };

  // Lấy dữ liệu user profile và business info
  const { data: userProfile } = useCurrentUser();
  const { data: businessInfo } = useBusinessInfo();

  // Use subscription payment hook V2
  const { handleCreatePayment, isCreatingPayment } = useSubscriptionPaymentV2();

  // Tạo defaultValues với dữ liệu user
  const formDefaultValues = useMemo(() => {
    // Xác định type dựa trên userProfile hoặc businessInfo
    const isBusinessUser = userProfile?.type === 'BUSINESS' || !!businessInfo;

    if (isBusinessUser && businessInfo) {
      return {
        ...defaultSubscriptionOrderValues,
        invoiceInfo: {
          type: InvoiceType.BUSINESS,
          representativeName: businessInfo.representativeName || '',
          representativePosition: businessInfo.representativePosition || '',
          companyName: businessInfo.businessName || '',
          companyAddress: businessInfo.businessAddress || '',
          taxCode: businessInfo.taxCode || '',
          email: userProfile?.email || '',
        },
      };
    }

    return {
      ...defaultSubscriptionOrderValues,
      invoiceInfo: {
        type: InvoiceType.PERSONAL,
        fullName: userProfile?.fullName || '',
        phoneNumber: userProfile?.phoneNumber || '',
        address: userProfile?.address || '',
        email: userProfile?.email || '',
      },
    };
  }, [userProfile, businessInfo]);

  // Mock discount for now (có thể implement coupon logic sau)
  const discount = 0;

  // Xử lý khi thanh toán
  const handleCheckout = async () => {
    // Kiểm tra planPricingId
    if (!packageInfo.planPricingId) {
      console.error('planPricingId is required for payment');
      return;
    }

    try {
      await handleCreatePayment(packageInfo.planPricingId);
    } catch (error) {
      console.error('Failed to create payment:', error);
    }
  };

  // Xử lý khi quay lại
  const handleBack = () => {
    navigate('/subscription/packages');
  };

  return (
    <PageWrapper>
      <Form
        schema={subscriptionOrderFormSchema}
        onSubmit={handleCheckout}
        defaultValues={formDefaultValues}
        className="space-y-6"
        mode="onBlur"
        validateOnBlur={true}
        validateOnChange={false}
      >
        <div className="flex flex-col lg:flex-row gap-6 mb-6">
          {/* Cột bên trái: Thông tin gói dịch vụ và thông tin xuất hóa đơn - chiếm hết chiều rộng còn lại */}
          <div className="flex-1 space-y-6">
            {/* Thông tin gói đã chọn */}
            <Card className="p-6">
              <Typography variant="h5" className="font-bold mb-4 flex items-center">
                <Icon name="package" size="md" className="mr-2 text-primary" />
                {t('subscription:order.selectedPackage', 'Gói dịch vụ đã chọn')}
              </Typography>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-600 rounded-lg flex items-center justify-center">
                    <Icon name="package" size="lg" className="text-white" />
                  </div>
                </div>

                <div className="flex-grow">
                  <Typography variant="h6" className="font-semibold mb-2">
                    {packageInfo.name}
                  </Typography>
                  <Typography variant="body2" className="text-muted mb-2">
                    {t(`subscription:serviceTypes.${packageInfo.type}`, packageInfo.type)}
                  </Typography>
                  <Typography variant="body2" className="text-muted">
                    {t(`subscription:duration.${packageInfo.duration}`, packageInfo.duration)}
                  </Typography>
                </div>

                <div className="text-right">
                  <Typography variant="h5" className="font-bold text-primary">
                    {formatPrice(packageInfo.price)}
                  </Typography>
                </div>
              </div>
            </Card>

            {/* Thông tin xuất hóa đơn */}
            <InvoiceInfo />

            {/* Phương thức thanh toán */}
            <PaymentMethodCard />
          </div>

          {/* Cột bên phải: Chi tiết đơn hàng - chiều rộng cố định */}
          <div className="w-full lg:w-96 space-y-6">
            {/* Chi tiết đơn hàng */}
            <OrderSummary
              packageName={packageInfo.name}
              packageType={packageInfo.type}
              duration={packageInfo.duration}
              price={packageInfo.price}
              discount={discount}
            />

            {/* Nút thanh toán */}
            <Button
              variant="primary"
              size="lg"
              fullWidth
              type="submit"
              isLoading={isCreatingPayment}
            >
              {t('subscription:order.checkout', 'Thanh toán')}
            </Button>

            {/* Nút quay lại */}
            <Button variant="outline" fullWidth onClick={handleBack} disabled={isCreatingPayment}>
              {t('subscription:order.back', 'Quay lại')}
            </Button>
          </div>
        </div>
      </Form>
    </PageWrapper>
  );
};

export default SubscriptionOrderPage;

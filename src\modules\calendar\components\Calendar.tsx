import React, { useCallback, useMemo, useEffect, useRef, useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { DateSelectArg, EventClickArg, EventChangeArg } from '@fullcalendar/core';
import { Button, Icon, IconCard } from '@/shared/components/common';
import { CalendarProps } from '../types';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';
import viLocale from '../locales/vi.fullcalendar';
import ViewSelect from './ViewSelect';
import '../styles/calendar.css';

const Calendar: React.FC<CalendarProps> = ({
  events = [],
  initialDate,
  initialView = 'dayGridMonth',
  weekends = true,
  editable = true,
  selectable = true,
  allDaySlot = false,
  height = 'auto',
  className = '',
  onDateSelect,
  onEventClick,
  onEventChange,
  onAddEvent,
  isCreating = false,
}) => {
  const { t } = useTranslation(['common', 'calendar']);
  const { themeMode } = useTheme();
  const calendarRef = useRef<FullCalendar>(null);
  const [currentView, setCurrentView] = useState<string>(initialView);

  // Use provided events only
  const displayEvents = useMemo(() => {
    console.log('📅 Calendar displayEvents:', events);
    console.log('📅 Events length:', events?.length);
    console.log('📅 Events array:', JSON.stringify(events, null, 2));

    // Always show real events if they exist
    if (events && events.length > 0) {
      console.log('📅 Using real events:', events);
      return events;
    }

    console.log('📅 No events to display');
    return [];
  }, [events]);

  // Handle date selection
  const handleDateSelect = useCallback(
    (selectInfo: DateSelectArg) => {
      if (onDateSelect) {
        onDateSelect(selectInfo);
      }
    },
    [onDateSelect]
  );

  // Handle event click
  const handleEventClick = useCallback(
    (clickInfo: EventClickArg) => {
      if (onEventClick) {
        onEventClick(clickInfo);
      }
    },
    [onEventClick]
  );

  // Handle event change (drag, resize)
  const handleEventChange = useCallback(
    (changeInfo: EventChangeArg) => {
      if (onEventChange) {
        onEventChange(changeInfo);
      }
    },
    [onEventChange]
  );

  // Handle view change from ViewSelect
  const handleViewChange = useCallback(
    (view: string) => {
      console.log('📅 View changing to:', view);
      console.log('📅 Current events:', displayEvents);
      setCurrentView(view);
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        apiInstance.changeView(view);

        // Force re-render events after view change
        setTimeout(() => {
          console.log('📅 Refetching events after view change');
          apiInstance.refetchEvents();
        }, 100);
      }
    },
    [displayEvents]
  );

  // Determine calendar class based on theme
  const calendarClass = `calendar-container ${themeMode === 'dark' ? 'fc-theme-dark' : 'fc-theme-light'} ${className}`;

  // Update currentView when calendar is initialized
  useEffect(() => {
    // Set initial view after calendar is initialized
    setTimeout(() => {
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        setCurrentView(apiInstance.view.type);

        // Update title initially
        const titleEl = document.getElementById('calendar-title');
        if (titleEl) {
          titleEl.textContent = apiInstance.view.title;
        }

        // Add event listener for view changes to update title
        apiInstance.on('datesSet', () => {
          const titleEl = document.getElementById('calendar-title');
          if (titleEl) {
            titleEl.textContent = apiInstance.view.title;
          }
        });
      }
    }, 500);
  }, []);

  // Ensure CSS is loaded and calendar is initialized properly
  useEffect(() => {
    // Sử dụng một timeout dài hơn để đảm bảo CSS được tải đầy đủ
    const timer = setTimeout(() => {
      const calendarEl = document.querySelector('.calendar-container');
      if (calendarEl) {
        calendarEl.classList.add('calendar-initialized');
      }

      // Kích hoạt resize event để FullCalendar tính toán lại kích thước
      window.dispatchEvent(new Event('resize'));

      // Sử dụng API của FullCalendar để render lại calendar
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        apiInstance.updateSize();
      }
    }, 500);

    // Thêm một resize handler để đảm bảo calendar luôn có kích thước đúng
    const handleResize = () => {
      const calendarEl = document.querySelector('.fc');
      if (calendarEl) {
        // Đảm bảo calendar chiếm toàn bộ chiều rộng của container
        (calendarEl as HTMLElement).style.width = '100%';
      }

      // Cập nhật kích thước của calendar khi cửa sổ thay đổi kích thước
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        apiInstance.updateSize();
      }
    };

    window.addEventListener('resize', handleResize);

    // Gọi resize handler ngay lập tức
    handleResize();

    // Thêm một timer bổ sung để đảm bảo calendar được render đúng kích thước
    const secondTimer = setTimeout(() => {
      handleResize();
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearTimeout(secondTimer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Re-render calendar when theme changes
  useEffect(() => {
    // Sử dụng key để buộc FullCalendar re-render hoàn toàn khi theme thay đổi
    const calendarEl = document.querySelector('.calendar-container');
    if (calendarEl) {
      // Đảm bảo calendar vẫn hiển thị khi thay đổi theme
      calendarEl.classList.add('calendar-initialized');

      // Thêm một class tạm thời để áp dụng transition
      calendarEl.classList.add('theme-transition');

      // Xóa class transition sau khi hoàn thành
      setTimeout(() => {
        calendarEl.classList.remove('theme-transition');

        // Kích hoạt resize event để FullCalendar tính toán lại kích thước
        window.dispatchEvent(new Event('resize'));

        // Đảm bảo calendar chiếm toàn bộ chiều rộng
        const fcEl = document.querySelector('.fc');
        if (fcEl) {
          (fcEl as HTMLElement).style.width = '100%';
        }

        // Sử dụng API của FullCalendar để cập nhật kích thước
        if (calendarRef.current) {
          const apiInstance = calendarRef.current.getApi();
          apiInstance.updateSize();
        }
      }, 300);
    }
  }, [themeMode]);

  return (
    <div className={calendarClass}>
      <div className="calendar-custom-header">
        <div className="calendar-custom-header-left">
          <Button
            variant="outline"
            size="sm"
            className="calendar-nav-button calendar-prev-button"
            onClick={() => calendarRef.current?.getApi().prev()}
          >
            <Icon name="chevron-left" size="sm" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="calendar-nav-button calendar-next-button"
            onClick={() => calendarRef.current?.getApi().next()}
          >
            <Icon name="chevron-right" size="sm" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="calendar-today-button"
            onClick={() => calendarRef.current?.getApi().today()}
          >
            {t('calendar:today', 'Hôm nay')}
          </Button>
        </div>
        <div className="calendar-custom-header-center">
          <h2 className="calendar-title" id="calendar-title"></h2>
        </div>
        <div className="calendar-custom-header-right">
          {onAddEvent && (
            <IconCard
              icon="plus"
              title={t('calendar:addEvent', 'Thêm sự kiện')}
              onClick={onAddEvent}
              disabled={isCreating}
              className="mr-2 bg-red-500 text-primary"
              size="sm"
            />
          )}
          <ViewSelect currentView={currentView} onViewChange={handleViewChange} />
        </div>
      </div>
      <FullCalendar
        ref={calendarRef}
        key={`calendar-${themeMode}`} // Thêm key để buộc re-render khi theme thay đổi
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
        initialView={initialView}
        {...(initialDate && { initialDate })}
        locale={viLocale}
        headerToolbar={false}
        buttonText={{
          today: t('calendar:today', 'Hôm nay'),
        }}
        events={displayEvents.map((event, index) => {
          console.log('📅 Mapping event for FullCalendar:', event);
          const calEvent = event as any; // Type assertion for flexibility

          // Build event object conditionally to avoid undefined values
          const eventInput: {
            id: string;
            title: string;
            start: string | Date;
            end?: string | Date;
            allDay?: boolean;
            backgroundColor?: string;
            borderColor?: string;
            textColor?: string;
            classNames?: string[];
            extendedProps?: any;
          } = {
            id: calEvent.id || `event-${index}`,
            title: calEvent.title || `Event ${index + 1}`,
            start: calEvent.start || calEvent.startTime || new Date(),
            classNames: [],
            extendedProps: {
              eventType: calEvent.eventType || 'task',
              priority: calEvent.priority || 'medium',
              description: calEvent.description || '',
            },
          };

          // Add event type class for styling
          const eventType = calEvent.eventType || 'task';
          eventInput.classNames = [`calendar-event-${eventType}`];

          // Only include optional properties if they have values
          if (calEvent.end !== undefined) {
            eventInput.end = calEvent.end;
          } else if (calEvent.endTime !== undefined) {
            eventInput.end = calEvent.endTime;
          }

          if (calEvent.allDay !== undefined) {
            eventInput.allDay = calEvent.allDay;
          }

          // Use gradient colors based on event type instead of solid colors
          const eventColors = {
            task: { bg: '#3b82f6', border: '#60a5fa' },
            report: { bg: '#10b981', border: '#34d399' },
            reminder: { bg: '#f59e0b', border: '#fbbf24' },
            meeting: { bg: '#8b5cf6', border: '#a78bfa' },
            deadline: { bg: '#ef4444', border: '#f87171' },
          };

          const colors = eventColors[eventType as keyof typeof eventColors] || eventColors.task;
          eventInput.backgroundColor = colors.bg;
          eventInput.borderColor = colors.border;
          eventInput.textColor = 'white';

          // Override with custom colors if provided
          if (calEvent.backgroundColor !== undefined) {
            eventInput.backgroundColor = calEvent.backgroundColor;
          }

          if (calEvent.borderColor !== undefined) {
            eventInput.borderColor = calEvent.borderColor;
          }

          if (calEvent.textColor !== undefined) {
            eventInput.textColor = calEvent.textColor;
          }

          if (calEvent.classNames !== undefined) {
            eventInput.classNames = [...eventInput.classNames, ...calEvent.classNames];
          }

          console.log('📅 Final eventInput for FullCalendar:', eventInput);
          return eventInput;
        })}
        editable={editable}
        selectable={selectable}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={weekends}
        allDaySlot={allDaySlot}
        height={height}
        // Event display settings for better visibility across all views
        eventDisplay="block"
        eventMaxStack={3}
        moreLinkClick="popover"
        dayMaxEventRows={3}
        // Time grid specific settings
        nowIndicator={true}
        scrollTime="08:00:00"
        slotMinTime="06:00:00"
        slotMaxTime="22:00:00"
        slotDuration="00:30:00"
        slotLabelInterval="01:00:00"
        // Event ordering
        eventOrder="start,-duration,allDay,title"
        eventOrderStrict={false}
        // Event callbacks
        select={handleDateSelect}
        eventClick={handleEventClick}
        eventChange={handleEventChange}
        eventTimeFormat={{
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }}
        slotLabelFormat={{
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }}
        eventDidMount={info => {
          // Apply event type styling
          const eventType = info.event.extendedProps?.eventType || 'task';
          const priority = info.event.extendedProps?.priority || 'medium';

          // Add data attributes for CSS targeting
          info.el.setAttribute('data-event-type', eventType);
          info.el.setAttribute('data-priority', priority);

          // Add CSS classes for styling
          info.el.classList.add(`calendar-event-${eventType}`);
          info.el.classList.add(`priority-${priority}`);

          // Add a subtle animation delay based on index for staggered appearance
          const eventIndex = Array.from(info.el.parentElement?.children || []).indexOf(info.el);
          info.el.style.animationDelay = `${eventIndex * 0.05}s`;

          // Add tooltip with event details
          const description = info.event.extendedProps?.description;
          if (description) {
            info.el.title = `${info.event.title}\n${description}`;
          } else {
            info.el.title = info.event.title;
          }
        }}
        themeSystem="standard"
        firstDay={1} // Bắt đầu từ thứ 2
        fixedWeekCount={false} // Số tuần hiển thị linh hoạt theo tháng
        showNonCurrentDates={false} // Ẩn các ngày không thuộc tháng hiện tại
      />
    </div>
  );
};

export default Calendar;

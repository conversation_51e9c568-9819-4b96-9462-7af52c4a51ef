import React, { useCallback, useMemo, useEffect, useRef, useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { DateSelectArg, EventClickArg, EventChangeArg } from '@fullcalendar/core';
import { Button, Icon, IconCard } from '@/shared/components/common';
import { CalendarProps } from '../types';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';
import viLocale from '../locales/vi.fullcalendar';
import ViewSelect from './ViewSelect';

const Calendar: React.FC<CalendarProps> = ({
  events = [],
  initialDate,
  initialView = 'dayGridMonth',
  weekends = true,
  editable = true,
  selectable = true,
  allDaySlot = false,
  height = 'auto',
  className = '',
  onDateSelect,
  onEventClick,
  onEventChange,
  onAddEvent,
  isCreating = false,
}) => {
  const { t } = useTranslation(['common', 'calendar']);
  const { themeMode } = useTheme();
  const calendarRef = useRef<FullCalendar>(null);
  const [currentView, setCurrentView] = useState<string>(initialView);

  // Use provided events only
  const displayEvents = useMemo(() => {
    console.log('📅 Calendar displayEvents:', events);
    console.log('📅 Events length:', events?.length);
    console.log('📅 Events array:', JSON.stringify(events, null, 2));

    // Always show real events if they exist
    if (events && events.length > 0) {
      console.log('📅 Using real events:', events);
      return events;
    }

    console.log('📅 No events to display');
    return [];
  }, [events]);

  // Handle date selection
  const handleDateSelect = useCallback(
    (selectInfo: DateSelectArg) => {
      if (onDateSelect) {
        onDateSelect(selectInfo);
      }
    },
    [onDateSelect]
  );

  // Handle event click
  const handleEventClick = useCallback(
    (clickInfo: EventClickArg) => {
      if (onEventClick) {
        onEventClick(clickInfo);
      }
    },
    [onEventClick]
  );

  // Handle event change (drag, resize)
  const handleEventChange = useCallback(
    (changeInfo: EventChangeArg) => {
      if (onEventChange) {
        onEventChange(changeInfo);
      }
    },
    [onEventChange]
  );

  // Handle view change from ViewSelect
  const handleViewChange = useCallback(
    (view: string) => {
      console.log('📅 View changing to:', view);
      console.log('📅 Current events:', displayEvents);
      setCurrentView(view);
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        apiInstance.changeView(view);

        // Force re-render events after view change
        setTimeout(() => {
          console.log('📅 Refetching events after view change');
          apiInstance.refetchEvents();
        }, 100);
      }
    },
    [displayEvents]
  );

  // Utility function for combining classes
  const cn = (...classes: (string | undefined)[]) => classes.filter(Boolean).join(' ');

  // Update currentView when calendar is initialized
  useEffect(() => {
    // Set initial view after calendar is initialized
    setTimeout(() => {
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        setCurrentView(apiInstance.view.type);

        // Update title initially
        const titleEl = document.getElementById('calendar-title');
        if (titleEl) {
          titleEl.textContent = apiInstance.view.title;
        }

        // Add event listener for view changes to update title
        apiInstance.on('datesSet', () => {
          const titleEl = document.getElementById('calendar-title');
          if (titleEl) {
            titleEl.textContent = apiInstance.view.title;
          }
        });
      }
    }, 500);
  }, []);

  // Ensure CSS is loaded and calendar is initialized properly
  useEffect(() => {
    // Sử dụng một timeout dài hơn để đảm bảo CSS được tải đầy đủ
    const timer = setTimeout(() => {
      const calendarEl = document.querySelector('.calendar-container');
      if (calendarEl) {
        calendarEl.classList.add('calendar-initialized');
      }

      // Kích hoạt resize event để FullCalendar tính toán lại kích thước
      window.dispatchEvent(new Event('resize'));

      // Sử dụng API của FullCalendar để render lại calendar
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        apiInstance.updateSize();
      }
    }, 500);

    // Thêm một resize handler để đảm bảo calendar luôn có kích thước đúng
    const handleResize = () => {
      const calendarEl = document.querySelector('.fc');
      if (calendarEl) {
        // Đảm bảo calendar chiếm toàn bộ chiều rộng của container
        (calendarEl as HTMLElement).style.width = '100%';
      }

      // Cập nhật kích thước của calendar khi cửa sổ thay đổi kích thước
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        apiInstance.updateSize();
      }
    };

    window.addEventListener('resize', handleResize);

    // Gọi resize handler ngay lập tức
    handleResize();

    // Thêm một timer bổ sung để đảm bảo calendar được render đúng kích thước
    const secondTimer = setTimeout(() => {
      handleResize();
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearTimeout(secondTimer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Re-render calendar when theme changes
  useEffect(() => {
    // Sử dụng key để buộc FullCalendar re-render hoàn toàn khi theme thay đổi
    const calendarEl = document.querySelector('.calendar-container');
    if (calendarEl) {
      // Đảm bảo calendar vẫn hiển thị khi thay đổi theme
      calendarEl.classList.add('calendar-initialized');

      // Thêm một class tạm thời để áp dụng transition
      calendarEl.classList.add('theme-transition');

      // Xóa class transition sau khi hoàn thành
      setTimeout(() => {
        calendarEl.classList.remove('theme-transition');

        // Kích hoạt resize event để FullCalendar tính toán lại kích thước
        window.dispatchEvent(new Event('resize'));

        // Đảm bảo calendar chiếm toàn bộ chiều rộng
        const fcEl = document.querySelector('.fc');
        if (fcEl) {
          (fcEl as HTMLElement).style.width = '100%';
        }

        // Sử dụng API của FullCalendar để cập nhật kích thước
        if (calendarRef.current) {
          const apiInstance = calendarRef.current.getApi();
          apiInstance.updateSize();
        }
      }, 300);
    }
  }, [themeMode]);

  return (
    <div className={cn('w-full h-full bg-white dark:bg-gray-900 rounded-xl shadow-lg', className)}>
      <div className="flex flex-wrap items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-t-xl">
        <div className="calendar-custom-header-left">
          <Button
            variant="outline"
            size="sm"
            className="calendar-nav-button calendar-prev-button"
            onClick={() => calendarRef.current?.getApi().prev()}
          >
            <Icon name="chevron-left" size="sm" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="calendar-nav-button calendar-next-button"
            onClick={() => calendarRef.current?.getApi().next()}
          >
            <Icon name="chevron-right" size="sm" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="calendar-today-button"
            onClick={() => calendarRef.current?.getApi().today()}
          >
            {t('calendar:today', 'Hôm nay')}
          </Button>
        </div>
        <div className="calendar-custom-header-center">
          <h2 className="calendar-title" id="calendar-title"></h2>
        </div>
        <div className="calendar-custom-header-right">
          {onAddEvent && (
            <IconCard
              icon="plus"
              title={t('calendar:addEvent', 'Thêm sự kiện')}
              onClick={onAddEvent}
              disabled={isCreating}
              className="mr-2 bg-red-500 text-primary"
              size="sm"
            />
          )}
          <ViewSelect currentView={currentView} onViewChange={handleViewChange} />
        </div>
      </div>
      <FullCalendar
        ref={calendarRef}
        key={`calendar-${themeMode}`} // Thêm key để buộc re-render khi theme thay đổi
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
        initialView={initialView}
        {...(initialDate && { initialDate })}
        locale={viLocale}
        headerToolbar={false}
        buttonText={{
          today: t('calendar:today', 'Hôm nay'),
        }}
        events={displayEvents.map((event, index) => {
          console.log('📅 Mapping event for FullCalendar:', event);
          const calEvent = event as any; // Type assertion for flexibility

          // Build event object conditionally to avoid undefined values
          const eventInput: {
            id: string;
            title: string;
            start: string | Date;
            end?: string | Date;
            allDay?: boolean;
            backgroundColor?: string;
            borderColor?: string;
            textColor?: string;
            classNames?: string[];
            extendedProps?: any;
          } = {
            id: calEvent.id || `event-${index}`,
            title: calEvent.title || `Event ${index + 1}`,
            start: calEvent.start || calEvent.startTime || new Date(),
            classNames: [],
            extendedProps: {
              eventType: calEvent.eventType || 'task',
              priority: calEvent.priority || 'medium',
              description: calEvent.description || '',
            },
          };

          // Add event type class for styling
          const eventType = calEvent.eventType || 'task';
          eventInput.classNames = [`calendar-event-${eventType}`];

          // Only include optional properties if they have values
          if (calEvent.end !== undefined) {
            eventInput.end = calEvent.end;
          } else if (calEvent.endTime !== undefined) {
            eventInput.end = calEvent.endTime;
          }

          if (calEvent.allDay !== undefined) {
            eventInput.allDay = calEvent.allDay;
          }

          // Use gradient colors based on event type instead of solid colors
          const eventColors = {
            task: { bg: '#3b82f6', border: '#60a5fa' },
            report: { bg: '#10b981', border: '#34d399' },
            reminder: { bg: '#f59e0b', border: '#fbbf24' },
            meeting: { bg: '#8b5cf6', border: '#a78bfa' },
            deadline: { bg: '#ef4444', border: '#f87171' },
          };

          const colors = eventColors[eventType as keyof typeof eventColors] || eventColors.task;
          eventInput.backgroundColor = colors.bg;
          eventInput.borderColor = colors.border;
          eventInput.textColor = 'white';

          // Override with custom colors if provided
          if (calEvent.backgroundColor !== undefined) {
            eventInput.backgroundColor = calEvent.backgroundColor;
          }

          if (calEvent.borderColor !== undefined) {
            eventInput.borderColor = calEvent.borderColor;
          }

          if (calEvent.textColor !== undefined) {
            eventInput.textColor = calEvent.textColor;
          }

          if (calEvent.classNames !== undefined) {
            eventInput.classNames = [...eventInput.classNames, ...calEvent.classNames];
          }

          console.log('📅 Final eventInput for FullCalendar:', eventInput);
          return eventInput;
        })}
        editable={editable}
        selectable={selectable}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={weekends}
        allDaySlot={allDaySlot}
        height={height}
        // Event display settings for better visibility across all views
        eventDisplay="block"
        eventMaxStack={3}
        moreLinkClick="popover"
        dayMaxEventRows={3}
        // Time grid specific settings
        nowIndicator={true}
        scrollTime="08:00:00"
        slotMinTime="06:00:00"
        slotMaxTime="22:00:00"
        slotDuration="00:30:00"
        slotLabelInterval="01:00:00"
        // Force events to be visible in all views
        eventConstraint={undefined}
        eventOverlap={true}
        selectOverlap={true}
        // Ensure events render properly in all views
        forceEventDuration={true}
        defaultAllDay={false}
        // Event ordering
        eventOrder="start,-duration,allDay,title"
        eventOrderStrict={false}
        // Event callbacks
        select={handleDateSelect}
        eventClick={handleEventClick}
        eventChange={handleEventChange}
        eventTimeFormat={{
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }}
        slotLabelFormat={{
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }}
        eventDidMount={info => {
          // Apply event type styling
          const eventType = info.event.extendedProps?.eventType || 'task';
          const priority = info.event.extendedProps?.priority || 'medium';

          // Add data attributes for CSS targeting
          info.el.setAttribute('data-event-type', eventType);
          info.el.setAttribute('data-priority', priority);

          // Add CSS classes for styling
          info.el.classList.add(`calendar-event-${eventType}`);
          info.el.classList.add(`priority-${priority}`);

          // Add a subtle animation delay based on index for staggered appearance
          const eventIndex = Array.from(info.el.parentElement?.children || []).indexOf(info.el);
          info.el.style.animationDelay = `${eventIndex * 0.05}s`;

          // Force event visibility in all views
          info.el.style.opacity = '1';
          info.el.style.visibility = 'visible';
          info.el.style.display = 'block';
          info.el.style.position = 'relative';
          info.el.style.zIndex = '1';

          // Additional fixes for time grid views
          if (currentView === 'timeGridWeek' || currentView === 'timeGridDay') {
            info.el.style.minHeight = '1.5rem';
            info.el.style.borderRadius = '8px';
            info.el.style.padding = '0.25rem 0.5rem';
            console.log('📅 Applied time grid styles to:', info.event.title);
          }

          // Add tooltip with event details
          const description = info.event.extendedProps?.description;
          if (description) {
            info.el.title = `${info.event.title}\n${description}`;
          } else {
            info.el.title = info.event.title;
          }

          console.log('📅 Event mounted:', info.event.title, 'in view:', currentView);
        }}
        themeSystem="standard"
        firstDay={1} // Bắt đầu từ thứ 2
        fixedWeekCount={false} // Số tuần hiển thị linh hoạt theo tháng
        showNonCurrentDates={false} // Ẩn các ngày không thuộc tháng hiện tại
      />

      {/* Tailwind CSS for FullCalendar */}
      <style jsx global>{`
        /* FullCalendar Base Styles */
        .fc {
          @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden;
        }

        /* Header Toolbar */
        .fc-toolbar {
          @apply flex flex-wrap items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700;
        }

        .fc-toolbar-title {
          @apply text-xl font-bold text-gray-900 dark:text-gray-100;
        }

        .fc-button-group {
          @apply flex rounded-lg overflow-hidden shadow-sm;
        }

        .fc-button {
          @apply px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-300 ease-in-out;
        }

        .fc-button:not(:last-child) {
          @apply border-r-0;
        }

        .fc-button-primary {
          @apply bg-blue-600 text-white border-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600;
        }

        .fc-button-active {
          @apply bg-blue-600 text-white border-blue-600 dark:bg-blue-500 shadow-inner;
        }

        /* Calendar Grid */
        .fc-view-harness {
          @apply bg-white dark:bg-gray-900;
        }

        .fc-col-header {
          @apply bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600;
        }

        .fc-col-header-cell {
          @apply p-3 text-sm font-semibold text-gray-700 dark:text-gray-300 text-center uppercase tracking-wide;
        }

        .fc-daygrid-day {
          @apply border-r border-b border-gray-200 dark:border-gray-700 min-h-[120px] bg-white dark:bg-gray-900 transition-colors duration-200;
        }

        .fc-daygrid-day:hover {
          @apply bg-gray-50 dark:bg-gray-800;
        }

        .fc-day-today {
          @apply bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700;
        }

        .fc-daygrid-day-number {
          @apply p-2 text-sm text-gray-700 dark:text-gray-300 font-medium hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full w-8 h-8 flex items-center justify-center transition-colors duration-200;
        }

        .fc-day-today .fc-daygrid-day-number {
          @apply bg-blue-600 text-white rounded-full font-bold shadow-md;
        }

        /* Events with smooth animations */
        .fc-event {
          @apply rounded-xl border-0 shadow-md transition-all duration-300 ease-out cursor-pointer overflow-hidden backdrop-blur-sm;
        }

        .fc-event:hover {
          @apply transform -translate-y-1 scale-105 shadow-xl rounded-2xl;
        }

        .fc-event:active {
          @apply transform translate-y-0 scale-100 shadow-lg rounded-xl transition-all duration-150;
        }

        .fc-daygrid-event {
          @apply mx-1 my-1 px-3 py-1.5 text-xs font-medium rounded-lg;
        }

        .fc-event-title {
          @apply font-semibold text-xs truncate;
        }

        .fc-event-time {
          @apply text-xs opacity-90 font-medium;
        }

        /* Time Grid */
        .fc-timegrid-slot {
          @apply h-14 border-b border-gray-100 dark:border-gray-800;
        }

        .fc-timegrid-axis {
          @apply bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700;
        }

        .fc-timegrid-slot-label {
          @apply text-xs text-gray-500 dark:text-gray-400 font-medium p-2;
        }

        .fc-timegrid-event {
          @apply rounded-lg shadow-md border-0 px-2 py-1 min-h-[28px] backdrop-blur-sm;
        }

        /* Enhanced List View */
        .fc-list-event {
          @apply flex items-center p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 dark:hover:from-gray-800 dark:hover:to-blue-900/20 transition-all duration-300 ease-out;
        }

        .fc-list-event:hover {
          @apply transform translate-x-2 border-l-4 border-l-blue-500 shadow-md;
        }

        .fc-list-event-dot {
          @apply w-3 h-3 rounded-full mr-4 flex-shrink-0 shadow-sm;
        }

        .fc-list-event-time {
          @apply text-sm font-bold text-blue-600 dark:text-blue-400 min-w-[80px] mr-4;
        }

        .fc-list-event-title {
          @apply text-sm font-semibold text-gray-900 dark:text-gray-100 flex-1 line-clamp-2;
        }

        .fc-list-day {
          @apply bg-gradient-to-r from-gray-100 via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 p-4 font-bold text-gray-900 dark:text-gray-100 border-b-2 border-gray-300 dark:border-gray-600 sticky top-0 z-10 shadow-sm;
        }

        .fc-list-empty {
          @apply p-16 text-center text-gray-500 dark:text-gray-400 italic;
        }

        .fc-list-empty::before {
          content: '📅';
          @apply block text-6xl mb-4 opacity-50;
        }

        /* Event Type Colors with gradients */
        .fc-event[data-event-type='task'] {
          @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-blue-200 dark:shadow-blue-900/50;
        }

        .fc-event[data-event-type='meeting'] {
          @apply bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-purple-200 dark:shadow-purple-900/50;
        }

        .fc-event[data-event-type='deadline'] {
          @apply bg-gradient-to-r from-red-500 to-red-600 text-white shadow-red-200 dark:shadow-red-900/50;
        }

        .fc-event[data-event-type='reminder'] {
          @apply bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-orange-200 dark:shadow-orange-900/50;
        }

        .fc-event[data-event-type='report'] {
          @apply bg-gradient-to-r from-green-500 to-green-600 text-white shadow-green-200 dark:shadow-green-900/50;
        }

        /* Priority Styles */
        .fc-event[data-priority='high'] {
          @apply ring-2 ring-red-300 dark:ring-red-600 ring-offset-1;
        }

        .fc-event[data-priority='medium'] {
          @apply ring-1 ring-gray-300 dark:ring-gray-600;
        }

        .fc-event[data-priority='low'] {
          @apply opacity-75;
        }

        /* Custom Scrollbar */
        .fc-scroller::-webkit-scrollbar {
          @apply w-2;
        }

        .fc-scroller::-webkit-scrollbar-track {
          @apply bg-gray-100 dark:bg-gray-800 rounded-full;
        }

        .fc-scroller::-webkit-scrollbar-thumb {
          @apply bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-gray-400 dark:hover:bg-gray-500;
        }

        /* More Link Styling */
        .fc-more-link {
          @apply text-blue-600 dark:text-blue-400 font-medium hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200;
        }

        /* Popover Styling */
        .fc-popover {
          @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl;
        }

        .fc-popover-header {
          @apply bg-gray-50 dark:bg-gray-700 p-3 border-b border-gray-200 dark:border-gray-600 font-semibold;
        }

        /* Now Indicator */
        .fc-timegrid-now-indicator-line {
          @apply border-red-500 shadow-sm;
        }

        .fc-timegrid-now-indicator-arrow {
          @apply border-red-500;
        }
      `}</style>
    </div>
  );
};

export default Calendar;

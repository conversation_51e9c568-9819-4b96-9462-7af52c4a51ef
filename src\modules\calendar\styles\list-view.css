/* Beautiful List View for FullCalendar */

/* List View Container */
.fc-list {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #eff6ff 100%);
  padding: 1rem;
  border-radius: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.dark .fc-list {
  background: linear-gradient(135deg, #111827 0%, #1f2937 50%, #111827 100%);
}

/* Remove default table styling */
.fc-list-table {
  border: 0;
  background: transparent;
  width: 100%;
  border-collapse: collapse;
}

.fc-list-table tbody tr {
  border: 0;
  background: transparent;
}

.fc-list-table tbody tr td {
  border: 0;
  background: transparent;
  padding: 0;
}

/* Beautiful List Events */
.fc-list-event {
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  align-items: center;
  padding: 1.25rem;
  margin: 0.5rem 1rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.dark .fc-list-event {
  background: #1f2937;
  border-color: #374151;
}

.fc-list-event:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 50%, #fce7f3 100%);
  border-left: 4px solid #3b82f6;
}

.dark .fc-list-event:hover {
  background: linear-gradient(135deg, #1e3a8a 0%, #312e81 50%, #581c87 100%);
}

.fc-list-event:hover td {
  background: transparent !important;
}

/* Event Dot */
.fc-list-event-dot {
  width: 1.25rem !important;
  height: 1.25rem !important;
  border-radius: 50% !important;
  margin-right: 1.25rem !important;
  flex-shrink: 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 3px solid white;
}

.dark .fc-list-event-dot {
  border-color: #1f2937;
}

/* Event Time */
.fc-list-event-time {
  font-size: 0.875rem;
  font-weight: 700;
  color: #2563eb;
  min-width: 90px;
  margin-right: 1.25rem;
  padding: 0.25rem 0.75rem;
  background: #dbeafe;
  border-radius: 9999px;
  text-align: center;
}

.dark .fc-list-event-time {
  color: #60a5fa;
  background: rgba(59, 130, 246, 0.2);
}

/* Event Title */
.fc-list-event-title {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  flex: 1;
  line-height: 1.5;
}

.dark .fc-list-event-title {
  color: #f9fafb;
}

/* Day Headers */
.fc-list-day {
  background: linear-gradient(135deg, #f3f4f6 0%, #f9fafb 50%, #f3f4f6 100%) !important;
  font-weight: 700 !important;
  color: #374151 !important;
  border: 0 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 20 !important;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-radius: 0.75rem;
  margin: 0.75rem 1rem;
  padding: 1rem;
}

.dark .fc-list-day {
  background: linear-gradient(135deg, #374151 0%, #4b5563 50%, #374151 100%) !important;
  color: #e5e7eb !important;
}

.fc-list-day td {
  border: 0 !important;
  background: transparent !important;
  padding: 0 !important;
}

.fc-list-day-text {
  font-size: 1.125rem !important;
  font-weight: 800 !important;
  color: #374151 !important;
}

.dark .fc-list-day-text {
  color: #e5e7eb !important;
}

.fc-list-day-side-text {
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #6b7280 !important;
  margin-left: 0.5rem !important;
}

.dark .fc-list-day-side-text {
  color: #9ca3af !important;
}

/* Empty State */
.fc-list-empty {
  padding: 5rem 1.25rem !important;
  text-align: center !important;
  color: #6b7280 !important;
  font-style: italic !important;
  background: linear-gradient(135deg, #f9fafb 0%, #eff6ff 100%);
  border-radius: 1rem;
  margin: 2rem 1rem;
}

.dark .fc-list-empty {
  color: #9ca3af !important;
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.fc-list-empty::before {
  content: '📅';
  display: block !important;
  font-size: 4rem !important;
  margin-bottom: 1.5rem !important;
  opacity: 0.3;
}

.fc-list-empty::after {
  content: 'Không có sự kiện nào trong khoảng thời gian này';
  display: block !important;
  font-size: 1.125rem !important;
  font-weight: 500 !important;
  color: #6b7280 !important;
  margin-top: 1rem !important;
}

.dark .fc-list-empty::after {
  color: #9ca3af !important;
}

/* Event Type Colors for Dots */
.fc-list-event[data-event-type="task"] .fc-list-event-dot {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.fc-list-event[data-event-type="meeting"] .fc-list-event-dot {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.fc-list-event[data-event-type="deadline"] .fc-list-event-dot {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.fc-list-event[data-event-type="reminder"] .fc-list-event-dot {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.fc-list-event[data-event-type="report"] .fc-list-event-dot {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .fc-list-event {
    padding: 1rem;
    margin: 0.25rem 0.5rem;
  }
  
  .fc-list-event-time {
    min-width: 70px;
    font-size: 0.75rem;
    margin-right: 1rem;
  }
  
  .fc-list-event-title {
    font-size: 0.875rem;
  }
  
  .fc-list-day {
    margin: 0.5rem 0.5rem;
    padding: 0.75rem;
  }
  
  .fc-list-day-text {
    font-size: 1rem !important;
  }
}

/* Animation for new events */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fc-list-event {
  animation: slideInUp 0.3s ease-out;
}

/* Hover animation */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

.fc-list-event:hover .fc-list-event-dot {
  animation: pulse 2s infinite;
}
